package org.flamesmplugin.commands;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.flamesmplugin.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Command handler for flame-related commands
 */
public class FlameCommand implements CommandExecutor, TabCompleter {
    private final FlameManager flameManager;
    private final FlameItems flameItems;
    
    public FlameCommand(FlameManager flameManager, FlameItems flameItems) {
        this.flameManager = flameManager;
        this.flameItems = flameItems;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "info":
                return handleInfo(sender, args);
            case "set":
                return handleSet(sender, args);
            case "upgrade":
                return handleUpgrade(sender, args);
            case "relight":
                return handleRelight(sender, args);
            case "give":
                return handleGive(sender, args);
            case "reset":
                return handleReset(sender, args);
            case "list":
                return handleList(sender);
            default:
                sendHelp(sender);
                return true;
        }
    }
    
    private boolean handleInfo(CommandSender sender, String[] args) {
        Player target;
        
        if (args.length > 1) {
            if (!sender.hasPermission("flame.admin")) {
                sender.sendMessage("§cYou don't have permission to check other players!");
                return true;
            }
            target = Bukkit.getPlayer(args[1]);
            if (target == null) {
                sender.sendMessage("§cPlayer not found!");
                return true;
            }
        } else {
            if (!(sender instanceof Player)) {
                sender.sendMessage("§cConsole must specify a player!");
                return true;
            }
            target = (Player) sender;
        }
        
        FlamePlayer flamePlayer = flameManager.getFlamePlayer(target);
        
        sender.sendMessage("§6=== Flame Info for " + target.getName() + " ===");
        if (flamePlayer.getFlameType() == null) {
            sender.sendMessage("§7No flame assigned");
        } else {
            sender.sendMessage("§7Flame: " + flamePlayer.getFlameType().getDisplayName());
            sender.sendMessage("§7Level: " + flamePlayer.getUpgradeLevel());
            sender.sendMessage("§7Deaths: " + flamePlayer.getDeaths() + "/3");
            sender.sendMessage("§7Extinguished: " + (flamePlayer.isFlameExtinguished() ? "§cYes" : "§aNo"));
            sender.sendMessage("§7Health: " + flamePlayer.getFlameHealth() + "/3");
        }
        
        return true;
    }
    
    private boolean handleSet(CommandSender sender, String[] args) {
        if (!sender.hasPermission("flame.admin")) {
            sender.sendMessage("§cYou don't have permission to use this command!");
            return true;
        }
        
        if (args.length < 3) {
            sender.sendMessage("§cUsage: /flame set <player> <flame_type>");
            return true;
        }
        
        Player target = Bukkit.getPlayer(args[1]);
        if (target == null) {
            sender.sendMessage("§cPlayer not found!");
            return true;
        }
        
        try {
            FlameType flameType = FlameType.valueOf(args[2].toUpperCase());
            FlamePlayer flamePlayer = flameManager.getFlamePlayer(target);
            flamePlayer.setFlameType(flameType);
            flamePlayer.setFlameExtinguished(false);
            flamePlayer.resetDeaths();
            flameManager.savePlayerData(flamePlayer);

            sender.sendMessage("§aSet " + target.getName() + "'s flame to " + flameType.getDisplayName());
            target.sendMessage("§6Your flame has been set to " + flameType.getDisplayName() + "!");

        } catch (IllegalArgumentException e) {
            StringBuilder validTypes = new StringBuilder();
            for (FlameType type : FlameType.values()) {
                if (validTypes.length() > 0) validTypes.append(", ");
                validTypes.append(type.name().toLowerCase());
            }
            sender.sendMessage("§cInvalid flame type! Valid types: " + validTypes.toString());
        }
        
        return true;
    }
    
    private boolean handleUpgrade(CommandSender sender, String[] args) {
        if (!sender.hasPermission("flame.admin")) {
            sender.sendMessage("§cYou don't have permission to use this command!");
            return true;
        }
        
        Player target;
        if (args.length > 1) {
            target = Bukkit.getPlayer(args[1]);
            if (target == null) {
                sender.sendMessage("§cPlayer not found!");
                return true;
            }
        } else {
            if (!(sender instanceof Player)) {
                sender.sendMessage("§cConsole must specify a player!");
                return true;
            }
            target = (Player) sender;
        }
        
        flameManager.upgradeFlame(target);
        sender.sendMessage("§aUpgraded " + target.getName() + "'s flame to Level 2!");
        
        return true;
    }
    
    private boolean handleRelight(CommandSender sender, String[] args) {
        if (!sender.hasPermission("flame.admin")) {
            sender.sendMessage("§cYou don't have permission to use this command!");
            return true;
        }
        
        Player target;
        if (args.length > 1) {
            target = Bukkit.getPlayer(args[1]);
            if (target == null) {
                sender.sendMessage("§cPlayer not found!");
                return true;
            }
        } else {
            if (!(sender instanceof Player)) {
                sender.sendMessage("§cConsole must specify a player!");
                return true;
            }
            target = (Player) sender;
        }
        
        flameManager.relightFlame(target);
        sender.sendMessage("§aRelit " + target.getName() + "'s flame!");
        
        return true;
    }
    
    private boolean handleGive(CommandSender sender, String[] args) {
        if (!sender.hasPermission("flame.admin")) {
            sender.sendMessage("§cYou don't have permission to use this command!");
            return true;
        }
        
        if (args.length < 3) {
            sender.sendMessage("§cUsage: /flame give <player> <item>");
            sender.sendMessage("§cItems: match, mace, upgrader");
            return true;
        }
        
        Player target = Bukkit.getPlayer(args[1]);
        if (target == null) {
            sender.sendMessage("§cPlayer not found!");
            return true;
        }
        
        String itemType = args[2].toLowerCase();
        ItemStack item;

        switch (itemType) {
            case "match":
                item = flameItems.createFlameMatch();
                break;
            case "mace":
                item = flameItems.createFlameMace();
                break;
            case "upgrader":
                item = flameItems.createFlameUpgrader();
                break;
            default:
                sender.sendMessage("§cInvalid item type! Valid types: match, mace, upgrader");
                return true;
        }

        target.getInventory().addItem(item);
        sender.sendMessage("§aGave " + target.getName() + " a " + itemType + "!");

        if (item.getItemMeta() != null && item.getItemMeta().hasDisplayName()) {
            Component displayName = item.getItemMeta().displayName();
            String legacyName = LegacyComponentSerializer.legacySection().serialize(displayName);
            target.sendMessage("§6You received a " + legacyName + "!");
        } else {
            target.sendMessage("§6You received a " + itemType + "!");
        }
        
        return true;
    }
    
    private boolean handleReset(CommandSender sender, String[] args) {
        if (!sender.hasPermission("flame.admin")) {
            sender.sendMessage("§cYou don't have permission to use this command!");
            return true;
        }
        
        if (args.length < 2) {
            sender.sendMessage("§cUsage: /flame reset <player>");
            return true;
        }
        
        Player target = Bukkit.getPlayer(args[1]);
        if (target == null) {
            sender.sendMessage("§cPlayer not found!");
            return true;
        }
        
        FlamePlayer flamePlayer = flameManager.getFlamePlayer(target);
        flamePlayer.setFlameType(null);
        flamePlayer.setUpgradeLevel(1);
        flamePlayer.resetDeaths();
        flamePlayer.setFlameExtinguished(false);
        flameManager.savePlayerData(flamePlayer);
        
        sender.sendMessage("§aReset " + target.getName() + "'s flame data!");
        target.sendMessage("§cYour flame data has been reset!");
        
        return true;
    }
    
    private boolean handleList(CommandSender sender) {
        sender.sendMessage("§6=== Available Flame Types ===");
        for (FlameType flameType : FlameType.values()) {
            sender.sendMessage("§7- " + flameType.getDisplayName());
            sender.sendMessage("  §8" + flameType.getDescription());
        }
        return true;
    }
    
    private void sendHelp(CommandSender sender) {
        sender.sendMessage("§6=== Flame Commands ===");
        sender.sendMessage("§7/flame info [player] - Show flame information");
        sender.sendMessage("§7/flame list - List all flame types");
        
        if (sender.hasPermission("flame.admin")) {
            sender.sendMessage("§c=== Admin Commands ===");
            sender.sendMessage("§7/flame set <player> <type> - Set player's flame");
            sender.sendMessage("§7/flame upgrade [player] - Upgrade flame to Level 2");
            sender.sendMessage("§7/flame relight [player] - Relight extinguished flame");
            sender.sendMessage("§7/flame give <player> <item> - Give custom items");
            sender.sendMessage("§7/flame reset <player> - Reset player's flame data");
        }
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            completions.addAll(Arrays.asList("info", "list"));
            if (sender.hasPermission("flame.admin")) {
                completions.addAll(Arrays.asList("set", "upgrade", "relight", "give", "reset"));
            }
        } else if (args.length == 2) {
            if (args[0].equalsIgnoreCase("set") || args[0].equalsIgnoreCase("upgrade") ||
                args[0].equalsIgnoreCase("relight") || args[0].equalsIgnoreCase("give") ||
                args[0].equalsIgnoreCase("reset") || args[0].equalsIgnoreCase("info")) {
                for (Player player : Bukkit.getOnlinePlayers()) {
                    completions.add(player.getName());
                }
            }
        } else if (args.length == 3) {
            if (args[0].equalsIgnoreCase("set")) {
                for (FlameType flameType : FlameType.values()) {
                    completions.add(flameType.name().toLowerCase());
                }
            } else if (args[0].equalsIgnoreCase("give")) {
                completions.addAll(Arrays.asList("match", "mace", "upgrader"));
            }
        }
        
        return completions;
    }
}
